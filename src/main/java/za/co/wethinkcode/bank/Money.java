package za.co.wethinkcode.bank;

import java.math.BigInteger;

public class Money implements Comparable<Money>
{
    public static final Money ZERO = new Money(0);

    private final BigInteger  randValue;
    private final int centValue;

    public Money(BigInteger randValue, int centValue ) {
        this.randValue = randValue;
        this.centValue = centValue;
    }

    public Money add(Money amount){
        return new Money( this.randValue.add(amount.randValue), centValue + amount.centValue );
    }

    public Money subtract(Money amount){
        return new Money( this.randValue.subtract(amount.randValue), centValue - amount.centValue );
    }

    public String formattedAsRands(){
        return "R" + randValue + "." + String.format("%02d", centValue);
    }
    @Override
    public int compareTo(Money money){
        return this.randValue.compareTo(money.randValue);
    }
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Money money = (Money) obj;
        return centValue == money.centValue && randValue.equals(money.randValue);
    }
    @Override
    public int hashCode() {
        return randValue.hashCode() + centValue;
    }
}
